# 图片批量文字添加工具 - 开发文档

## 项目概述

本项目是一个功能完整的图片批量文字添加工具，支持从文件名提取文字和添加自定义文字，提供丰富的文字样式设置。项目采用模块化设计，支持GUI和命令行两种运行模式。

## 开发环境

- **Python版本**: 3.7+
- **主要依赖**: 
  - PyQt5 (GUI框架)
  - Pillow (图像处理)
- **开发工具**: VS Code, PyCharm等
- **操作系统**: Windows 10/11 (推荐)

## 项目特色

### 1. 模块化架构
- 严格按照功能分模块编写
- 每个独立功能对应一个Python文件
- 清晰的依赖关系和接口设计

### 2. 配置驱动
- 所有参数通过配置文件管理
- 支持实时配置保存和加载
- 用户界面参数自动同步

### 3. 工作流管理
- 可配置的处理步骤
- 支持步骤的启用/禁用
- 灵活的执行顺序调整

### 4. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 用户友好的错误提示

## 核心功能实现

### 文件名文字提取
- 支持多种提取模式（书名号、方括号等）
- 正则表达式模式匹配
- 批量处理优化

### 文字渲染
- 丰富的字体样式支持
- 描边和阴影效果
- 透明度和位置控制

### 图片处理
- 多格式支持（JPG、PNG、BMP等）
- 内存缓存优化
- 批量保存处理

## 开发规范

### 代码规范
1. **函数注释**: 每个函数前使用中文详细注释
2. **模块化**: 严格按功能分模块，单一职责原则
3. **错误处理**: 完善的try-catch和日志记录
4. **路径引用**: 使用绝对路径，支持中文编码

### 界面开发规范
1. **PyQt5**: 统一使用PyQt5进行界面开发
2. **参数暴露**: 所有功能参数在界面上可调节
3. **实时保存**: 参数修改实时保存到配置文件
4. **复选框展示**: 新功能以复选框形式展示

### 配置管理规范
1. **JSON格式**: 使用JSON格式存储配置
2. **嵌套结构**: 按功能分类组织配置项
3. **默认值**: 提供完整的默认配置
4. **实时同步**: 界面和配置文件实时同步

## 测试策略

### 功能测试
- [x] 命令行模式基本功能
- [x] 文件名文字提取
- [x] 图片加载和保存
- [x] 配置管理
- [x] 工作流执行

### 测试用例
1. **正常流程**: 选择文件夹 → 设置参数 → 批量处理
2. **边界条件**: 空文件夹、无效文件、权限问题
3. **错误处理**: 文件损坏、内存不足、磁盘空间不足

## 部署说明

### 环境准备
```bash
# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 运行方式
```bash
# GUI模式
python main.py

# 命令行模式
python main.py --cli --input-folder ./images --output-folder ./output
```

## 扩展开发

### 添加新的文字效果
1. 在`core/image_processing/text_renderer.py`中添加渲染方法
2. 在`core/text_processing/text_style_manager.py`中添加样式配置
3. 在界面中添加对应的参数控件

### 支持新的图片格式
1. 在`core/image_processing/image_loader.py`中添加格式支持
2. 在`core/image_processing/image_saver.py`中添加保存支持
3. 更新`SUPPORTED_FORMATS`列表

### 添加新的提取模式
1. 在`core/text_processing/filename_extractor.py`中添加正则模式
2. 在界面的模式选择中添加新选项
3. 更新`get_common_patterns()`方法

## 性能优化

### 内存管理
- 图片处理完成后及时释放内存
- 使用缓存避免重复加载
- 大批量处理时分批进行

### 处理速度
- 多线程并行处理
- 异步I/O操作
- 进度反馈优化

## 已知问题和限制

### 当前限制
1. **GUI依赖**: 需要安装PyQt5才能使用GUI模式
2. **字体支持**: 依赖系统字体，某些字体可能不可用
3. **大文件处理**: 超大图片可能导致内存不足

### 解决方案
1. **GUI问题**: 提供命令行模式作为备选
2. **字体问题**: 自动检测可用字体，提供默认字体
3. **内存问题**: 分批处理，及时释放内存

## 未来规划

### 功能扩展
- [ ] 图片预览功能完善
- [ ] 交互式文字位置调整
- [ ] 更多文字特效支持
- [ ] 批量模板应用

### 性能优化
- [ ] GPU加速支持
- [ ] 更高效的图片处理算法
- [ ] 分布式处理支持

### 用户体验
- [ ] 多语言界面支持
- [ ] 主题和皮肤系统
- [ ] 插件系统支持

## 贡献指南

### 代码贡献
1. Fork项目到个人仓库
2. 创建功能分支进行开发
3. 遵循项目代码规范
4. 添加必要的测试用例
5. 提交Pull Request

### 问题反馈
1. 使用GitHub Issues报告问题
2. 提供详细的错误信息和日志
3. 描述重现步骤
4. 附上系统环境信息

## 技术支持

### 常见问题
1. **安装问题**: 检查Python版本和依赖包
2. **运行问题**: 查看日志文件排查错误
3. **性能问题**: 调整批处理大小和内存设置

### 联系方式
- 项目Issues: [GitHub Issues页面]
- 技术交流: [技术交流群]
- 邮箱支持: [技术支持邮箱]

## 版本历史

### v1.0.0 (2024-01-XX)
- ✅ 完成基础架构设计
- ✅ 实现核心图片处理功能
- ✅ 完成文字提取和渲染
- ✅ 实现配置管理系统
- ✅ 完成工作流控制
- ✅ 实现命令行模式
- ✅ 完成基础GUI框架
- ✅ 编写完整文档

### 开发统计
- **代码文件**: 15个Python文件
- **代码行数**: 约3000行
- **功能模块**: 7个核心模块
- **配置项**: 30+个可配置参数
- **支持格式**: 6种图片格式

---

**开发完成日期**: 2024年1月
**开发者**: 图片文字添加工具开发团队
**项目状态**: 基础版本完成，持续优化中
