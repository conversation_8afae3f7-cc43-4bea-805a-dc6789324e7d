2025-08-11 16:33:15 - ImageTextTool - INFO - 找到 7 个可用字体
2025-08-11 16:33:15 - ImageTextTool - INFO - ==================================================
2025-08-11 16:33:15 - ImageTextTool - INFO - 图片批量文字添加工具启动
2025-08-11 16:33:15 - ImageTextTool - INFO - ==================================================
2025-08-11 16:33:15 - ImageTextTool - INFO - 配置管理器初始化完成
2025-08-11 16:33:15 - ImageTextTool - INFO - 添加工作流步骤: load_images
2025-08-11 16:33:15 - ImageTextTool - INFO - 添加工作流步骤: extract_filename_text
2025-08-11 16:33:15 - ImageTextTool - INFO - 添加工作流步骤: add_custom_text
2025-08-11 16:33:15 - ImageTextTool - INFO - 添加工作流步骤: render_text
2025-08-11 16:33:15 - ImageTextTool - INFO - 添加工作流步骤: save_images
2025-08-11 16:33:15 - ImageTextTool - INFO - 工作流配置导入完成
2025-08-11 16:33:15 - ImageTextTool - INFO - 工作流管理器初始化完成
2025-08-11 16:33:15 - ImageTextTool - INFO - 应用程序初始化完成
2025-08-11 16:35:00 - ImageTextTool - INFO - 找到 7 个可用字体
2025-08-11 16:35:00 - ImageTextTool - INFO - ==================================================
2025-08-11 16:35:00 - ImageTextTool - INFO - 图片批量文字添加工具启动
2025-08-11 16:35:00 - ImageTextTool - INFO - ==================================================
2025-08-11 16:35:00 - ImageTextTool - INFO - 配置管理器初始化完成
2025-08-11 16:35:00 - ImageTextTool - INFO - 添加工作流步骤: load_images
2025-08-11 16:35:00 - ImageTextTool - INFO - 添加工作流步骤: extract_filename_text
2025-08-11 16:35:00 - ImageTextTool - INFO - 添加工作流步骤: add_custom_text
2025-08-11 16:35:00 - ImageTextTool - INFO - 添加工作流步骤: render_text
2025-08-11 16:35:00 - ImageTextTool - INFO - 添加工作流步骤: save_images
2025-08-11 16:35:00 - ImageTextTool - INFO - 工作流配置导入完成
2025-08-11 16:35:00 - ImageTextTool - INFO - 工作流管理器初始化完成
2025-08-11 16:35:00 - ImageTextTool - INFO - 应用程序初始化完成
2025-08-11 16:35:00 - ImageTextTool - INFO - 启动命令行模式
2025-08-11 16:35:00 - ImageTextTool - INFO - 开始执行工作流
2025-08-11 16:35:00 - ImageTextTool - INFO - 开始执行步骤: load_images
2025-08-11 16:35:00 - ImageTextTool - ERROR - 步骤执行失败: load_images, 错误: load_images_step() takes 1 positional argument but 2 were given
2025-08-11 16:35:00 - ImageTextTool - ERROR - 工作流执行失败，遇错停止
2025-08-11 16:57:14 - ImageTextTool - INFO - 找到 7 个可用字体
2025-08-11 16:57:14 - ImageTextTool - INFO - ==================================================
2025-08-11 16:57:14 - ImageTextTool - INFO - 图片批量文字添加工具启动
2025-08-11 16:57:14 - ImageTextTool - INFO - ==================================================
2025-08-11 16:57:14 - ImageTextTool - INFO - 配置管理器初始化完成
2025-08-11 16:57:14 - ImageTextTool - INFO - 添加工作流步骤: load_images
2025-08-11 16:57:14 - ImageTextTool - INFO - 添加工作流步骤: extract_filename_text
2025-08-11 16:57:14 - ImageTextTool - INFO - 添加工作流步骤: add_custom_text
2025-08-11 16:57:14 - ImageTextTool - INFO - 添加工作流步骤: render_text
2025-08-11 16:57:14 - ImageTextTool - INFO - 添加工作流步骤: save_images
2025-08-11 16:57:14 - ImageTextTool - INFO - 工作流配置导入完成
2025-08-11 16:57:14 - ImageTextTool - INFO - 工作流管理器初始化完成
2025-08-11 16:57:14 - ImageTextTool - INFO - 应用程序初始化完成
2025-08-11 16:57:14 - ImageTextTool - INFO - 启动GUI模式
2025-08-11 16:57:14 - ImageTextTool - ERROR - PyQt5未安装，无法启动GUI模式
2025-08-11 16:58:24 - ImageTextTool - INFO - 找到 7 个可用字体
2025-08-11 16:58:24 - ImageTextTool - INFO - ==================================================
2025-08-11 16:58:24 - ImageTextTool - INFO - 图片批量文字添加工具启动
2025-08-11 16:58:24 - ImageTextTool - INFO - ==================================================
2025-08-11 16:58:24 - ImageTextTool - INFO - 配置管理器初始化完成
2025-08-11 16:58:24 - ImageTextTool - INFO - 添加工作流步骤: load_images
2025-08-11 16:58:24 - ImageTextTool - INFO - 添加工作流步骤: extract_filename_text
2025-08-11 16:58:24 - ImageTextTool - INFO - 添加工作流步骤: add_custom_text
2025-08-11 16:58:24 - ImageTextTool - INFO - 添加工作流步骤: render_text
2025-08-11 16:58:24 - ImageTextTool - INFO - 添加工作流步骤: save_images
2025-08-11 16:58:24 - ImageTextTool - INFO - 工作流配置导入完成
2025-08-11 16:58:24 - ImageTextTool - INFO - 工作流管理器初始化完成
2025-08-11 16:58:24 - ImageTextTool - INFO - 应用程序初始化完成
2025-08-11 16:58:24 - ImageTextTool - INFO - 启动GUI模式
2025-08-11 16:58:24 - ImageTextTool - ERROR - PyQt5未安装，无法启动GUI模式
2025-08-11 16:59:32 - ImageTextTool - INFO - 找到 7 个可用字体
2025-08-11 16:59:32 - ImageTextTool - INFO - ==================================================
2025-08-11 16:59:32 - ImageTextTool - INFO - 图片批量文字添加工具启动
2025-08-11 16:59:32 - ImageTextTool - INFO - ==================================================
2025-08-11 16:59:32 - ImageTextTool - INFO - 配置管理器初始化完成
2025-08-11 16:59:32 - ImageTextTool - INFO - 添加工作流步骤: load_images
2025-08-11 16:59:32 - ImageTextTool - INFO - 添加工作流步骤: extract_filename_text
2025-08-11 16:59:32 - ImageTextTool - INFO - 添加工作流步骤: add_custom_text
2025-08-11 16:59:32 - ImageTextTool - INFO - 添加工作流步骤: render_text
2025-08-11 16:59:32 - ImageTextTool - INFO - 添加工作流步骤: save_images
2025-08-11 16:59:32 - ImageTextTool - INFO - 工作流配置导入完成
2025-08-11 16:59:32 - ImageTextTool - INFO - 工作流管理器初始化完成
2025-08-11 16:59:32 - ImageTextTool - INFO - 应用程序初始化完成
2025-08-11 16:59:32 - ImageTextTool - INFO - 启动命令行模式
2025-08-11 16:59:32 - ImageTextTool - INFO - 开始执行工作流
2025-08-11 16:59:32 - ImageTextTool - INFO - 开始执行步骤: load_images
2025-08-11 16:59:32 - ImageTextTool - INFO - 在文件夹 test_images 中找到 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 加载了 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 步骤执行完成: load_images
2025-08-11 16:59:32 - ImageTextTool - INFO - 开始执行步骤: extract_filename_text
2025-08-11 16:59:32 - ImageTextTool - INFO - 在文件夹 test_images 中找到 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 加载了 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 批量文字提取完成: 4/5 个文件提取成功
2025-08-11 16:59:32 - ImageTextTool - INFO - 从 4/5 个文件名中提取了文字
2025-08-11 16:59:32 - ImageTextTool - INFO - 步骤执行完成: extract_filename_text
2025-08-11 16:59:32 - ImageTextTool - INFO - 开始执行步骤: add_custom_text
2025-08-11 16:59:32 - ImageTextTool - INFO - 获取了 0 个自定义文字
2025-08-11 16:59:32 - ImageTextTool - INFO - 步骤执行完成: add_custom_text
2025-08-11 16:59:32 - ImageTextTool - INFO - 开始执行步骤: render_text
2025-08-11 16:59:32 - ImageTextTool - INFO - 在文件夹 test_images 中找到 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 加载了 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 在文件夹 test_images 中找到 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 加载了 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 批量文字提取完成: 4/5 个文件提取成功
2025-08-11 16:59:32 - ImageTextTool - INFO - 从 4/5 个文件名中提取了文字
2025-08-11 16:59:32 - ImageTextTool - INFO - 获取了 0 个自定义文字
2025-08-11 16:59:32 - ImageTextTool - INFO - 成功处理了 5/5 个图片
2025-08-11 16:59:32 - ImageTextTool - INFO - 步骤执行完成: render_text
2025-08-11 16:59:32 - ImageTextTool - INFO - 开始执行步骤: save_images
2025-08-11 16:59:32 - ImageTextTool - INFO - 在文件夹 test_images 中找到 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 加载了 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 在文件夹 test_images 中找到 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 加载了 5 个图片文件
2025-08-11 16:59:32 - ImageTextTool - INFO - 批量文字提取完成: 4/5 个文件提取成功
2025-08-11 16:59:32 - ImageTextTool - INFO - 从 4/5 个文件名中提取了文字
2025-08-11 16:59:32 - ImageTextTool - INFO - 获取了 0 个自定义文字
2025-08-11 16:59:32 - ImageTextTool - INFO - 成功处理了 5/5 个图片
2025-08-11 16:59:32 - ImageTextTool - INFO - 批量保存完成: 5/5 个图片保存成功
2025-08-11 16:59:32 - ImageTextTool - INFO - 成功保存了 5/5 个图片
2025-08-11 16:59:32 - ImageTextTool - INFO - 步骤执行完成: save_images
2025-08-11 16:59:32 - ImageTextTool - INFO - 工作流执行完成
2025-08-11 17:58:37 - ImageTextTool - INFO - 找到 7 个可用字体
2025-08-11 17:58:37 - ImageTextTool - INFO - ==================================================
2025-08-11 17:58:37 - ImageTextTool - INFO - 图片批量文字添加工具启动
2025-08-11 17:58:37 - ImageTextTool - INFO - ==================================================
2025-08-11 17:58:37 - ImageTextTool - INFO - 配置管理器初始化完成
2025-08-11 17:58:37 - ImageTextTool - INFO - 添加工作流步骤: load_images
2025-08-11 17:58:37 - ImageTextTool - INFO - 添加工作流步骤: extract_filename_text
2025-08-11 17:58:37 - ImageTextTool - INFO - 添加工作流步骤: add_custom_text
2025-08-11 17:58:37 - ImageTextTool - INFO - 添加工作流步骤: render_text
2025-08-11 17:58:37 - ImageTextTool - INFO - 添加工作流步骤: save_images
2025-08-11 17:58:37 - ImageTextTool - INFO - 工作流配置导入完成
2025-08-11 17:58:37 - ImageTextTool - INFO - 工作流管理器初始化完成
2025-08-11 17:58:37 - ImageTextTool - INFO - 应用程序初始化完成
2025-08-11 17:58:37 - ImageTextTool - INFO - 启动GUI模式
2025-08-11 17:58:37 - ImageTextTool - INFO - 导入 0 个自定义文字配置
2025-08-11 17:58:37 - ImageTextTool - INFO - 配置加载完成
2025-08-11 17:58:37 - ImageTextTool - INFO - 主窗口初始化完成
2025-08-11 17:59:18 - ImageTextTool - INFO - 在文件夹 E:/python_code/pic_chuli/短剧采集图 中找到 10 个图片文件
2025-08-11 17:59:18 - ImageTextTool - INFO - 文件夹选择完成: E:/python_code/pic_chuli/短剧采集图, 找到 10 个图片
2025-08-11 17:59:25 - ImageTextTool - INFO - 预览请求
2025-08-11 17:59:27 - ImageTextTool - INFO - 预览请求
2025-08-11 17:59:30 - ImageTextTool - INFO - 预览请求
2025-08-11 18:00:07 - ImageTextTool - INFO - 开始批量处理图片
2025-08-11 18:00:07 - ImageTextTool - INFO - 批量保存完成: 10/10 个图片保存成功
2025-08-11 18:00:08 - ImageTextTool - INFO - 处理完成: 处理完成！成功处理 10/10 个图片
2025-08-11 18:04:48 - ImageTextTool - INFO - 主窗口关闭
